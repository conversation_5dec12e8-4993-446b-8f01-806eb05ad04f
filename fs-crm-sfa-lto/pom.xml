<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-recycling-task</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-crm-sfa-lto</artifactId>
    <version>9.6.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <appframework.version>9.6.0-SNAPSHOT</appframework.version>
        <metadata-provider.version>9.6.0-SNAPSHOT</metadata-provider.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-plat-privilege-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-data-ext</artifactId>
            <version>${appframework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-provider</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>dispatcher-support</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>http-spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-admin-api</artifactId>
        </dependency>
        <!-- 金融云 引用开放平台SDK -->
        <dependency>
            <groupId>cn.com.antcloud.api</groupId>
            <artifactId>antcloud-api-sdk</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>cn.com.antcloud.api</groupId>
            <artifactId>antcloud-api-riskplus</artifactId>
            <version>1.12.6</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-expression</artifactId>
            <version>8.8.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata</artifactId>
            <version>${appframework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-provider</artifactId>
            <version>${metadata-provider.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <version>${appframework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-common</artifactId>
            <version>${appframework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-web</artifactId>
            <version>${appframework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-restdriver</artifactId>
            <version>${appframework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege</artifactId>
            <version>${appframework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege-temp</artifactId>
            <version>${appframework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-fcp</artifactId>
            <version>${appframework.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-config</artifactId>
            <version>${appframework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-ai-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-provider</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-fsi-proxy</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-paas-expression</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>i18n-util</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-gnomon-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-sfa-expression-provider</artifactId>
            <version>${appframework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-provider</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-task-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-rest-client-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Mandatory plugins for using Spock -->
            <!--使用Spock的强制性插件 -->
            <plugin>
                <!-- The gmavenplus plugin is used to compile Groovy code. To learn more about this plugin,visit https://github.com/groovy/GMavenPlus/wiki -->
                <!-- 这个 gmavenplus 插件是用于编译Groovy代码的 . 想获取更多此插件相关信息,visit https://github.com/groovy/GMavenPlus/wiki -->
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>