package com.facishare.crm.sfa.lto.duplicated;

import com.github.autoconf.ConfigFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * ClearDuplicatedProcessor性能配置类
 * 用于管理重复数据清理处理器的性能参数
 */
@Component
@Data
@Slf4j
public class ClearDuplicatedProcessorConfig {
    
    // 最大处理深度
    private int maxProcessingDepth = 5; // 从10降到5
    
    // 最大处理数量
    private int maxProcessingSize = 5000; // 从10000降到5000
    
    // 最大处理时间（毫秒）
    private long maxProcessingTimeMs = 120000; // 从5分钟降到2分钟
    
    // 批处理大小
    private int batchSize = 10; // 从20降到10
    
    // 数据查询最大数量
    private int maxDataIds = 200; // 从1000降到200
    
    // 重复数据查询限制
    private int duplicateDataLimit = 50; // 从100降到50
    
    // 是否启用性能优化模式
    private boolean performanceOptimizationEnabled = true;
    
    // 缓存访问超时时间（毫秒）
    private long cacheAccessTimeoutMs = 5000; // 5秒
    
    // 数据库查询超时时间（毫秒）
    private long dbQueryTimeoutMs = 10000; // 10秒
    
    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-duplicated-processor-config", config -> {
            maxProcessingDepth = config.getInt("maxProcessingDepth", 5);
            maxProcessingSize = config.getInt("maxProcessingSize", 5000);
            maxProcessingTimeMs = config.getLong("maxProcessingTimeMs", 120000L);
            batchSize = config.getInt("batchSize", 10);
            maxDataIds = config.getInt("maxDataIds", 200);
            duplicateDataLimit = config.getInt("duplicateDataLimit", 50);
            performanceOptimizationEnabled = config.getBool("performanceOptimizationEnabled", true);
            cacheAccessTimeoutMs = config.getLong("cacheAccessTimeoutMs", 5000L);
            dbQueryTimeoutMs = config.getLong("dbQueryTimeoutMs", 10000L);
            
            log.info("ClearDuplicatedProcessor config loaded: maxDepth={}, maxSize={}, maxTime={}ms, batchSize={}, maxDataIds={}, duplicateLimit={}, optimizationEnabled={}", 
                maxProcessingDepth, maxProcessingSize, maxProcessingTimeMs, batchSize, maxDataIds, duplicateDataLimit, performanceOptimizationEnabled);
        });
    }
    
    /**
     * 检查是否应该跳过处理（基于性能考虑）
     */
    public boolean shouldSkipProcessing(long startTime, int processedCount) {
        if (!performanceOptimizationEnabled) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        
        // 超时检查
        if (currentTime - startTime > maxProcessingTimeMs) {
            log.warn("Processing timeout: {}ms > {}ms", (currentTime - startTime), maxProcessingTimeMs);
            return true;
        }
        
        // 处理数量检查
        if (processedCount > maxProcessingSize) {
            log.warn("Processing size limit exceeded: {} > {}", processedCount, maxProcessingSize);
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取调整后的批处理大小（基于当前负载）
     */
    public int getAdjustedBatchSize() {
        // 可以根据当前系统负载动态调整
        return batchSize;
    }
    
    /**
     * 获取调整后的数据查询限制
     */
    public int getAdjustedMaxDataIds() {
        return maxDataIds;
    }
}
