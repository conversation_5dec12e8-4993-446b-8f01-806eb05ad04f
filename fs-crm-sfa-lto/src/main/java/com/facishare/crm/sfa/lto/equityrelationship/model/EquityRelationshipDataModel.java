package com.facishare.crm.sfa.lto.equityrelationship.model;


import lombok.Data;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2023/6/29 15:53
 */

public interface EquityRelationshipDataModel {

    @Data
    class Result {
        private boolean success = true;
        private String msg;
    }
    @Data
    class Arg {
        private String name;
    }
    @Data
    class DataSyncArg {
        private Object before;
        private Meta meta;
        private Object after;
        private Keys keys;
    }
    @Data
    class Meta {
        private String schema;
        private String op;
        private String time;
        private String db;
        private String table;
    }
    @Data
    class Keys {
        private String tenant_id;
        private String id;
    }
    @Data
    class QueryResult {
        private Integer code ;
        private String status;
        private String message;
        private Object data;
        private RelationshipModel Result;
    }

    @Data
    class RelationshipModel {
        private String eid ;
        private String identifier;
        private List<Trees> c_trees;
        private String name;
        private List<Trees> p_trees;
        private String short_name;
        private String type;
        private String has_problem;
        private Integer p_count;
        private Integer c_count;
    }
    @Data
    class Trees {
        private String eid ;
        private String identifier;
        private String amount;
        private String sh_type;
        private String name;
        private String level;
        private String short_name;
        private String type;
        private String has_problem;
        private String percent;
        private List<Trees> items;
    }

//    @Data
//    class QueryBranchResult {
//        private Integer code ;
//        private String status;
//        private String message;
//        private BranchInfo data;
//    }

    @Data
    class BranchInfo {
        private ExecutionInfo execution_info;
        private BaseInfo base_info;
        private ChainRelation chain_relation;
    }
    @Data
    class ExecutionInfo {
        private List<ExecutionItemsInfo> items;
    }
    @Data
    class ExecutionItemsInfo {
        private String date;
        private String number;
        private String province;
        private String oper_name;
        private String execution_status;
        private String final_duty;
        private String doc_number;
        private String case_number;
        private String execution_desc;
        private String court;
        private String ex_department;
        private String publish_date;
    }
    @Data
    class BaseInfo {
        private String end_date;
        private String reg_no;
        private String city;
        private String province;
        private String check_date;
        private String scope;
        private Contact contact;
        private String belong_org;
        private String term_end;
        private Object abnormal_items;
        private String org_no;
        private String econ_kind;
        private String start_date;
        private Object changerecords;
        private String address;
        private String oper_name;
        private List<String> domains;
        private List<Employees> branches;
        private String credit_no;
        private String term_start;
        private List<PartnersItem> partners;
        private String name;
        private List<Employees> employees;
        private String regist_capi;
        private String status;
    }
    @Data
    class Contact {
        private String address;
        private String telephone;
        private String email;
    }
    @Data
    class PartnersItem {
        private String stock_type;
        private String identify_no;
        private Object real_capi_items;
        private String name;
        private String identify_type;
        private Object should_capi_items;
    }
    @Data
    class Employees {
        private String name;
        private String job_title;
    }
    @Data
    class ChainRelation {
        private NodeInfo node_info;
        private InfoVo info;
    }
    @Data
    class NodeInfo {
        private Object notices;
        private List<Lawsuits> lawsuits;
        private List<Lawsuits> partners;
        private List<Lawsuits> suspected_relation;
        private List<Lawsuits> employees;
        private Object investments;
        private Object history_partners;
    }


    @Data
    class Lawsuits {
        private String name;
        private String title;
        private String short_name;
        private String id;
        private String percent;
        private List<RelatedBy> related_by;

    }
    @Data
    class RelatedBy {
        private String type;
        private String content;
    }
    @Data
    class InfoVo {
        private String address;
        private String reg_no;
        private String reg_capi;
        private String oper_name;
        private String telephone;
        private String capi_unit;
        private String credit_no;
        private String domain;
        private String name;
        private String short_name;
        private String id;
        private String org_no;
        private String status;
        private String start_date;
    }
    @Data
    class CompanyDetailResult {
        private Integer code ;
        private String status;
        private String message;
        private Object data;
        private CompanyDetail Result;
    }
    @Data
    class CompanyDetail {
        private String end_date;
        private String reg_no;
        private String city;
        private String new_status;
        private Object quoted_type;
        private String province;
        private String check_date;
        private String scope;
        private Contact contact;
        private String belong_org;
        private String term_end;
        private String id;
        private Object abnormal_items;
        private String org_no;
        private String econ_kind;
        private String econ_kind_code;
        private String start_date;
        private Object changerecords;
        private String address;
        private String oper_name;
        private Object domains;
        private Object history_names;
        private List<Lawsuits> branches;
        private String credit_no;
        private String term_start;
        private Object partners;
        private String district_code;
        private String name;
        private List<Websites> websites;
        private Object employees;
        private String is_quoted;
        private String regist_capi;
        private String format_name;
        private String status;
    }

    @Data
    class Websites {
        private String web_name;
        private String web_type;
        private String web_url;
        private String source;
        private String seq_no;
        private String date;

    }
    @Data
    class RelationshipCreateMsg {
        private String objectApiName;
        private String objectId;
        private String name;
        private String tenantId;
        private String redisRequestId;
        private String userId;
        private Integer objectType;
        private String appId;
        private List<String> opportunityIds;
        private List<String> salesOrderIds;

    }





}
