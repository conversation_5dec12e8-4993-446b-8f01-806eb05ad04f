package com.facishare.crm.sfa.lto.rest;

import com.facishare.crm.sfa.lto.equityrelationship.model.EquityRelationshipDataModel;
import com.facishare.crm.sfa.lto.industry.CompanyModel;
import com.facishare.crm.sfa.lto.rest.models.IndustryCompanyAdvance;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2024/3/6 20:20
 */
@RestResource(value = "INDUSTRY_INTERFACE", desc = "CRM Rest API Call", contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface SFAIndustryInterfaceProxy {

    @GET(value = "/industry/recommendFcpService/v1/group/getFamilyTree?keyword={name}")
    EquityRelationshipDataModel.QueryResult getFamilyTree(@HeaderMap Map<String, String> headers, @PathParam("name") String name);
    @GET(value = "/industry/recommendFcpService/v1/company/companyDetail?keyword={name}")
    EquityRelationshipDataModel.CompanyDetailResult companyDetail(@HeaderMap Map<String, String> headers, @PathParam("name") String name);

    @POST(value = "/industry/recommendFcpService/getCompanyDetailByName")
    CompanyModel.CompanyDetailResult getCompanyDetailByName(@HeaderMap Map<String, String> headers, @Body CompanyModel.CompanyDetailArg body);

    @POST(value = "/industry/industryFcpService/getCompanyAdvance")
    IndustryCompanyAdvance.CompanyAdvanceResult getCompanyAdvance(@HeaderMap Map<String, String> headers, @Body IndustryCompanyAdvance.Arg body);

}
