package com.facishare.crm.sfa.lto.duplicated;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * ClearDuplicatedProcessor监控组件
 * 用于监控处理性能和检测潜在的性能问题
 */
@Component
@Slf4j
public class ClearDuplicatedProcessorMonitor {
    
    // 当前正在处理的任务数
    private final AtomicInteger activeProcessingCount = new AtomicInteger(0);
    
    // 总处理次数
    private final AtomicLong totalProcessingCount = new AtomicLong(0);
    
    // 超时次数
    private final AtomicLong timeoutCount = new AtomicLong(0);
    
    // 错误次数
    private final AtomicLong errorCount = new AtomicLong(0);
    
    // 正在处理的任务信息 <traceId, startTime>
    private final ConcurrentHashMap<String, Long> activeProcessing = new ConcurrentHashMap<>();
    
    // 最近的处理时间统计
    private final ConcurrentHashMap<String, Long> recentProcessingTimes = new ConcurrentHashMap<>();
    
    @Autowired
    private ClearDuplicatedProcessorConfig config;
    
    /**
     * 开始处理
     */
    public void startProcessing(String traceId) {
        activeProcessingCount.incrementAndGet();
        totalProcessingCount.incrementAndGet();
        activeProcessing.put(traceId, System.currentTimeMillis());
        
        log.info("[Monitor] Start processing {}, activeCount: {}, totalCount: {}", 
            traceId, activeProcessingCount.get(), totalProcessingCount.get());
    }
    
    /**
     * 完成处理
     */
    public void completeProcessing(String traceId, long processingTime) {
        activeProcessingCount.decrementAndGet();
        activeProcessing.remove(traceId);
        recentProcessingTimes.put(traceId, processingTime);
        
        // 保持最近100条记录
        if (recentProcessingTimes.size() > 100) {
            String oldestKey = recentProcessingTimes.keySet().iterator().next();
            recentProcessingTimes.remove(oldestKey);
        }
        
        log.info("[Monitor] Complete processing {}, time: {}ms, activeCount: {}", 
            traceId, processingTime, activeProcessingCount.get());
            
        // 检查是否超时
        if (processingTime > config.getMaxProcessingTimeMs()) {
            timeoutCount.incrementAndGet();
            log.warn("[Monitor] TIMEOUT detected for {}, time: {}ms, timeoutCount: {}", 
                traceId, processingTime, timeoutCount.get());
        }
    }
    
    /**
     * 记录错误
     */
    public void recordError(String traceId, Exception e) {
        errorCount.incrementAndGet();
        log.error("[Monitor] ERROR in processing {}, errorCount: {}", traceId, errorCount.get(), e);
    }
    
    /**
     * 记录超时
     */
    public void recordTimeout(String traceId) {
        timeoutCount.incrementAndGet();
        log.warn("[Monitor] TIMEOUT recorded for {}, timeoutCount: {}", traceId, timeoutCount.get());
    }
    
    /**
     * 检查是否有长时间运行的任务
     */
    public void checkLongRunningTasks() {
        long currentTime = System.currentTimeMillis();
        long warningThreshold = config.getMaxProcessingTimeMs() / 2; // 超过一半时间就警告
        
        activeProcessing.forEach((traceId, startTime) -> {
            long runningTime = currentTime - startTime;
            if (runningTime > warningThreshold) {
                log.warn("[Monitor] LONG RUNNING task detected: {}, runningTime: {}ms", traceId, runningTime);
            }
        });
    }
    
    /**
     * 获取统计信息
     */
    public String getStatistics() {
        long avgProcessingTime = 0;
        if (!recentProcessingTimes.isEmpty()) {
            avgProcessingTime = recentProcessingTimes.values().stream()
                .mapToLong(Long::longValue)
                .sum() / recentProcessingTimes.size();
        }
        
        return String.format(
            "Monitor Statistics - Active: %d, Total: %d, Timeout: %d, Error: %d, AvgTime: %dms",
            activeProcessingCount.get(),
            totalProcessingCount.get(),
            timeoutCount.get(),
            errorCount.get(),
            avgProcessingTime
        );
    }
    
    /**
     * 检查系统是否过载
     */
    public boolean isSystemOverloaded() {
        // 如果活跃任务数超过阈值，认为系统过载
        int maxConcurrentTasks = 10; // 可配置
        boolean tooManyActiveTasks = activeProcessingCount.get() > maxConcurrentTasks;
        
        // 如果超时率过高，认为系统过载
        double timeoutRate = totalProcessingCount.get() > 0 ? 
            (double) timeoutCount.get() / totalProcessingCount.get() : 0;
        boolean highTimeoutRate = timeoutRate > 0.1; // 超过10%超时率
        
        if (tooManyActiveTasks || highTimeoutRate) {
            log.warn("[Monitor] SYSTEM OVERLOADED - activeTasks: {}, timeoutRate: {:.2f}%", 
                activeProcessingCount.get(), timeoutRate * 100);
            return true;
        }
        
        return false;
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalProcessingCount.set(0);
        timeoutCount.set(0);
        errorCount.set(0);
        recentProcessingTimes.clear();
        log.info("[Monitor] Statistics reset");
    }
}
