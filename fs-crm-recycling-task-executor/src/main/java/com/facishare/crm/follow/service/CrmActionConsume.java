package com.facishare.crm.follow.service;

import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.model.CrmActionMQMessage;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.util.FollowGrayUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.util.CrmActionMessageConvert;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.facishare.crm.follow.util.CommonUtils.buildContext;
import static com.facishare.crm.recycling.task.executor.util.ObjectDataUtils.buildUser;

/**
 * <AUTHOR> gongchunru
 * @date : 2023/12/1 14:55
 * @description:
 */
@Component
@Slf4j
public class CrmActionConsume {

    @Autowired
    private CustomerFollowService customerFollowService;

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private FollowSettingCacheService followSettingCacheService;

    @Autowired
    private CommonService commonService;

    private FsGrayReleaseBiz dataSfagray = FsGrayRelease.getInstance("sfa");


    /**
     * @param messageObj
     */
    @SFAAuditLog(bizName = "#bizName", entityClass = CrmActionMQMessage.class, convertClass = CrmActionMessageConvert.class, status = "#status",
            messageId = "#msg.msgId", extra = "#msg.reconsumeTimes", cost1 = "#cost1", extra1 = "#extra1", extra2 = "#extra2", message = "#message", extra3 = "#extra3",
            condition = "#status == true")
    public void newConsumeMessage(CrmActionMQMessage messageObj) {
        String tenantId = messageObj.getTenantID();
        String objectId = messageObj.getObjectID();
        String describeApiName = messageObj.getObjectApiName();
        IObjectData objectData = null;
        ActionCodeEnum actionCode = ActionCodeEnum.actionCodeOf(messageObj.getActionCode());
        String settingType = null;
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();
        UpdateFollowDealModel.Content content;
        Long currentTime = System.currentTimeMillis();
        String operatorID = messageObj.getOperatorID();
        if (FollowGrayUtils.isEnableAuditLog){
            SFALogContext.putVariable("status", true);
        }else {
            SFALogContext.putVariable("status", false);
        }

        if (Utils.LEADS_API_NAME.equals(describeApiName)) {
            Map<String, String> map = commonService.getObjectFollowDealSettingType(tenantId,
                    Lists.newArrayList(describeApiName),
                    actionCode);
            if (map != null && map.get(Utils.LEADS_API_NAME) != null) {
                content = commonService.buildContent(objectId, Utils.LEADS_API_NAME, null, currentTime, messageObj.getEventId(), messageObj.getOperatorID());
                leadsContents.add(content);
            } else {
                sendLeadsObjRecalculateTask(messageObj);
                return;
            }
        } else {
            if (Utils.ACCOUNT_API_NAME.equals(describeApiName) && (actionCode.getActionCode().equals("Allocate") || actionCode.getActionCode().equals("allocate"))) {
                settingType = customerFollowService.getSettingTypeByActionCodes(tenantId,
                        Utils.ACCOUNT_API_NAME, Utils.ACCOUNT_API_NAME,
                        Lists.newArrayList("Allocate", "allocate"));
            } else {
                settingType = customerFollowService.getObjectFollowDealSettingType(tenantId,
                        describeApiName,
                        actionCode);
            }
            if (settingType == null) {
                log.info("settingType is null objectId:{},tenantId:{}", objectId, tenantId);
                SFALogContext.putVariable("message", "settingType is null");
                return;
            }

            if ("1".equals(settingType)) {
                content = commonService.buildContent(objectId, Utils.ACCOUNT_API_NAME, null, currentTime, messageObj.getEventId(), operatorID);
                contents.add(content);
            }
        }

        //附件上传，获取客户id
        if (ActionCodeEnum.ADD_ATTACH.getActionCode().equals(messageObj.getActionCode())) {
            CrmActionMQMessage.Content actionContent = messageObj.getActionContentSmart();
            if (actionContent != null) {
                objectId = actionContent.getId();
            }
        }

        if (!Utils.ACCOUNT_API_NAME.equals(describeApiName)) {
            try {
                if ("1".equals(settingType) || "0".equals(settingType)) {
                    objectData = objectDataPgService.findById(objectId, tenantId, buildContext(buildUser(tenantId)), describeApiName);
                    if (objectData == null) {
                        log.warn("objectData is null,apiname:{}", describeApiName);
                        return;
                    }
                    List<ObjectReferenceFieldDescribe> accountObjs = customerFollowService
                            .objectReferenceFieldDescribe(tenantId, objectId, describeApiName);
                    for (ObjectReferenceFieldDescribe accountObj : accountObjs) {
                        if (objectData.get(accountObj.getApiName()) == null || ("").equals(objectData.get(accountObj.getApiName()))) {
                            log.info("accountObj.getApiName is null");
                            continue;
                        }
                        objectId = objectData.get(accountObj.getApiName()).toString();
                        content = commonService.buildContent(objectId, accountObj.getTargetApiName(),
                                null, currentTime, messageObj.getEventId(), operatorID);
                        contents.add(content);
                    }
                }
            } catch (MetadataServiceException e) {
                log.error("MetadataServiceException ", e);
                throw new RuntimeException(e);
            }
        }
        SFALogContext.putVariable("extra1", contents.toString());
        SFALogContext.putVariable("extra2", leadsContents.toString());
        SFALogContext.putVariable("status", true);
        updateFollowDealModel.setContents(contents);
        customerFollowService.updateFollowDealTime(updateFollowDealModel);

        updateFollowDealModel.setContents(leadsContents);
        customerFollowService.updateFollowDealTime(updateFollowDealModel);

    }


    /**
     * @param messageObj
     */
    @SFAAuditLog(bizName = "#bizName", entityClass = CrmActionMQMessage.class, convertClass = CrmActionMessageConvert.class, status = "#status",
            messageId = "#msg.msgId", extra = "#msg.reconsumeTimes", extra1 = "#extra1", extra2 = "#extra2", message = "#message", extra3 = "#extra3",
            condition = "#status == true")
    public void consumeMessageGray(CrmActionMQMessage messageObj) {
        String tenantId = messageObj.getTenantID();
        String objectId = messageObj.getObjectID();
        if (ActionCodeEnum.ADD_ATTACH.getActionCode().equals(messageObj.getActionCode())) {
            CrmActionMQMessage.Content actionContent = messageObj.getActionContentSmart();
            if (actionContent != null) {
                objectId = actionContent.getId();
            }
        }
        String describeApiName = messageObj.getObjectApiName();
        ActionCodeEnum actionCode = ActionCodeEnum.actionCodeOf(messageObj.getActionCode());
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();
        SFALogContext.putVariable("status", false);

        Set<String> settingApiNames = followSettingCacheService.getFollowSetting(tenantId, describeApiName, actionCode);
        log.info("objectId:{},describeApiName:{},actionCode:{},settingApiNames:{},",  objectId, describeApiName, actionCode,settingApiNames);
        boolean hasAccountFollowSetting = settingApiNames != null && settingApiNames.contains(Utils.ACCOUNT_API_NAME);
        boolean hasLeadsFollowSetting = settingApiNames != null && settingApiNames.contains(Utils.LEADS_API_NAME);

        // 没有跟进配置的情况下也需要计算线索的预计收回时间
        if (Utils.LEADS_API_NAME.equals(describeApiName)) {
            handleLeadsCase(messageObj, hasLeadsFollowSetting, leadsContents);
            if (!hasLeadsFollowSetting && !hasAccountFollowSetting) {
                return;
            }
        }

        if (settingApiNames == null) {
            log.info("followSetting is null id:{},{},{}", objectId, describeApiName, actionCode);
            return;
        }

        if (Utils.ACCOUNT_API_NAME.equals(describeApiName)) {
            handleAccountCase(messageObj, hasAccountFollowSetting, contents);
            if (!hasLeadsFollowSetting && !hasAccountFollowSetting) {
                return;
            }
        }

        if (!Utils.ACCOUNT_API_NAME.equals(describeApiName) && !Utils.LEADS_API_NAME.equals(describeApiName)) {
            handleOtherCase(messageObj, hasAccountFollowSetting, contents);
        }

        SFALogContext.putVariable("status", true);
        updateFollowDealModel.setContents(contents);
        SFALogContext.putVariable("extra1", contents.toString());
        customerFollowService.updateFollowDealTime(updateFollowDealModel);

        updateFollowDealModel.setContents(leadsContents);
        SFALogContext.putVariable("extra2", leadsContents.toString());
        customerFollowService.updateFollowDealTime(updateFollowDealModel);
    }

    private void handleLeadsCase(CrmActionMQMessage messageObj, boolean hasLeadsFollowSetting, List<UpdateFollowDealModel.Content> leadsContents) {
        if (hasLeadsFollowSetting) {
            leadsContents.add(commonService.buildContent(messageObj.getObjectID(), Utils.LEADS_API_NAME, null,
                    System.currentTimeMillis(), messageObj.getEventId(), messageObj.getOperatorID()));
        } else {
            sendLeadsObjRecalculateTask(messageObj);
        }
    }

    // todo 多余
    private void handleAccountCase(CrmActionMQMessage messageObj, boolean hasAccountFollowSetting, List<UpdateFollowDealModel.Content> contents) {
        ActionCodeEnum actionCode = ActionCodeEnum.actionCodeOf(messageObj.getActionCode());
        if (!hasAccountFollowSetting && actionCode != null && "Allocate".equals(actionCode.getActionCode())) {
            hasAccountFollowSetting = followSettingCacheService.getFollowSetting(messageObj.getTenantID(), messageObj.getObjectApiName(), Utils.ACCOUNT_API_NAME, ActionCodeEnum.ALLOCATE_OLD);
            if (!hasAccountFollowSetting) {
                log.info("hasAccountFollowSetting is false {},actionCode:{}", messageObj.getObjectApiName(), actionCode);
                SFALogContext.putVariable("message", "hasAccountFollowSetting is false " + actionCode.getActionCode());
                return;
            }
        }
        if (hasAccountFollowSetting) {
            contents.add(commonService.buildContent(messageObj.getObjectID(), Utils.ACCOUNT_API_NAME, null,
                    System.currentTimeMillis(), messageObj.getEventId(), messageObj.getOperatorID()));
        }
    }

    private void handleOtherCase(CrmActionMQMessage messageObj, boolean hasAccountFollowSetting, List<UpdateFollowDealModel.Content> contents) {
        try {
            if (hasAccountFollowSetting) {
                IObjectData objectData = objectDataPgService.findById(messageObj.getObjectID(), messageObj.getTenantID(),
                        buildContext(buildUser(messageObj.getTenantID())), messageObj.getObjectApiName());
                if (objectData == null) {
                    log.warn("objectData is null,apiname:{}", messageObj.getObjectApiName());
                    return;
                }
                List<ObjectReferenceFieldDescribe> accountObjs = customerFollowService
                        .objectReferenceFieldDescribe(messageObj.getTenantID(), messageObj.getObjectID(), messageObj.getObjectApiName());
                accountObjs.stream()
                        .filter(accountObj -> objectData.get(accountObj.getApiName()) != null && !("").equals(objectData.get(accountObj.getApiName())))
                        .forEach(accountObj -> {
                            String objectId = objectData.get(accountObj.getApiName()).toString();
                            contents.add(commonService.buildContent(objectId, accountObj.getTargetApiName(),
                                    null, System.currentTimeMillis(), messageObj.getEventId(), messageObj.getOperatorID()));
                        });
            }
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException ", e);
            throw new RuntimeException(e);
        }
    }

    private void sendLeadsObjRecalculateTask(CrmActionMQMessage messageObj) {
        if (!Utils.LEADS_API_NAME.equals(messageObj.getObjectApiName())){
            return;
        }
        if (ActionCodeEnum.MOVE.getActionCode().equals(messageObj.getActionCode())
                || ActionCodeEnum.ALLOCATE.getActionCode().equals(messageObj.getActionCode())) {
            log.info("leadsObj not setting followed send:{},{}", messageObj.getActionCode(), messageObj.getObjectID());
            customerFollowService.sendRecalculateTask(messageObj.getTenantID(), Utils.LEADS_API_NAME, messageObj.getObjectID());
            return;
        }
        log.info("leadsObj not setting followed send:{},{}", messageObj.getActionCode(), messageObj.getObjectID());
    }


    public Boolean skipTenantId(String tenantId) {
        return dataSfagray.isAllow("skip_object_edit_follow_tenant_id", tenantId);
    }
}
